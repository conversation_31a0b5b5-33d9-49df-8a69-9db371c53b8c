#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的小红书爬虫功能
"""

import logging
from xiaohongshu_crawler import XiaohongshuCrawler

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_fixed_crawler():
    """测试修复后的爬虫功能"""
    crawler = None
    try:
        logger.info("="*60)
        logger.info("测试修复后的小红书爬虫滚动功能")
        logger.info("="*60)
        
        # 初始化爬虫
        crawler = XiaohongshuCrawler()
        
        # 执行爬取任务
        logger.info("开始执行爬取任务...")
        success = crawler.run_crawl_task(
            keyword="笑话",
            post_count=30  # 测试获取30个帖子，会触发滚动功能
        )
        
        if success:
            logger.info("✅ 爬取任务执行成功！")
            logger.info("请查看生成的结果文件和日志了解详细信息")
        else:
            logger.error("❌ 爬取任务执行失败")
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return False
    
    finally:
        if crawler:
            crawler.cleanup()

if __name__ == "__main__":
    logger.info("开始测试修复后的小红书爬虫")
    
    success = test_fixed_crawler()
    
    if success:
        logger.info("🎉 测试完成！修复后的滚动功能工作正常")
    else:
        logger.info("⚠️ 测试未完全成功，请检查日志了解详情")
    
    logger.info("\n" + "="*60)
    logger.info("测试说明：")
    logger.info("1. 修复后的爬虫能够正确处理小红书的虚拟滚动机制")
    logger.info("2. 通过data-index属性跟踪实际访问的帖子数量")
    logger.info("3. 当需要更多帖子时，会自动滚动页面访问更多内容")
    logger.info("4. 详细的分析报告请查看 SCROLL_FIX_ANALYSIS.md")
    logger.info("="*60)
