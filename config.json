{"crawler_settings": {"default_keyword": "笑话", "default_post_count": 20, "headless_mode": false, "timeout_ms": 30000, "login_wait_time": 300, "delay_between_posts": 2, "delay_after_click": 3, "page_load_delay": 3, "login_check_interval": 5}, "selectors": {"search_input": "#search-input", "note_items": ".note-item", "note_links": "a[href*='/explore/']", "post_title": "h1, .title, [class*='title']", "post_content": ".note-content, .content, [class*='content'], .desc, [class*='desc']", "author_name": ".author-info .name, .user-info .name, .author .name, [class*='author'] [class*='name'], [class*='user'] [class*='name']", "publish_time": ".publish-time, .time, [class*='time'], .date, [class*='date']", "like_count": ".like-count, [class*='like'] .count, [class*='like'], .interaction-count", "comment_count": ".comment-count, [class*='comment'] .count, [class*='comment']", "post_images": "img", "post_tags": ".tag, [class*='tag']"}, "output_settings": {"file_format": "json", "output_file": "xiaoh<PERSON><PERSON>_crawl_results.json", "images_folder": "images", "include_timestamp": true, "encoding": "utf-8"}, "database": {"enabled": true, "db_path": "xiaohongshu_crawl.db", "use_database": true, "fallback_to_json": false}, "anti_detection": {"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "viewport": {"width": 1280, "height": 720}, "random_delays": {"min": 1, "max": 3}}, "image_download": {"enabled": true, "max_images_per_post": 10, "image_formats": ["jpg", "jpeg", "png", "webp"], "timeout_seconds": 30}, "deduplication": {"enabled": true, "storage_file": "crawled_posts.json", "storage_format": "json"}}